{"name": "backend", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon server.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@apollo/server": "^4.11.3", "@graphql-tools/schema": "^10.0.20", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "cloudinary": "^2.5.1", "cors": "^2.8.5", "dataloader": "^2.2.3", "dotenv": "^16.4.7", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "graphql": "^16.10.0", "graphql-subscriptions": "^2.0.0", "graphql-tag": "^2.12.6", "graphql-upload": "^15.0.0", "graphql-ws": "^5.16.2", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mammoth": "^1.9.0", "mime-types": "^2.1.35", "mongodb": "^6.14.0", "mongoose": "^8.11.0", "mongoose-lean-virtuals": "^1.1.0", "multer": "^1.4.5-lts.1", "nodemailer": "^7.0.3", "or": "^0.2.0", "pdf-parse": "^1.1.1", "sharp": "^0.34.1", "validator": "^13.15.0", "winston": "^3.17.0", "ws": "^8.18.1", "yup": "^1.6.1"}, "devDependencies": {"nodemon": "^3.1.9"}}