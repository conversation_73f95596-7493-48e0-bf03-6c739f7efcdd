/* Styles pour le composant d'évaluation de projet */
.container {
  max-width: 1200px;
  margin: 0 auto;
}

.form-group {
  margin-bottom: 1rem;
}

.error-message {
  color: #dc3545;
  margin-top: 0.25rem;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  margin: 2rem 0;
}

/* Animations personnalisées pour le formulaire */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(79, 95, 173, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(79, 95, 173, 0.6), 0 0 30px rgba(79, 95, 173, 0.4);
  }
}

@keyframes glowDark {
  0%, 100% {
    box-shadow: 0 0 5px rgba(0, 247, 255, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(0, 247, 255, 0.6), 0 0 30px rgba(0, 247, 255, 0.4);
  }
}

/* Styles pour les inputs avec focus amélioré */
.form-input-enhanced {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.form-input-enhanced:focus {
  transform: translateY(-2px);
}

/* Styles pour les boutons avec effet hover */
.btn-modern {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-modern:hover::before {
  left: 100%;
}

/* Styles pour les cartes avec effet glassmorphism */
.glass-card {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass-card {
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Animation pour les éléments qui apparaissent */
.animate-fade-in {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in {
  animation: slideInRight 0.6s ease-out;
}

/* Styles pour la barre de progression */
.progress-bar-animated {
  background: linear-gradient(90deg, #4f5fad, #7826b5);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.dark .progress-bar-animated {
  background: linear-gradient(90deg, #00f7ff, #9d4edd);
  background-size: 200% 100%;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Styles pour les tooltips */
.tooltip {
  position: relative;
}

.tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s;
  z-index: 1000;
}

.tooltip:hover::after {
  opacity: 1;
}

/* Responsive design amélioré */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .grid-responsive {
    grid-template-columns: 1fr;
  }

  .btn-modern {
    width: 100%;
    justify-content: center;
  }
}

/* Styles pour les états de focus accessibles */
.focus-visible:focus {
  outline: 2px solid #4f5fad;
  outline-offset: 2px;
}

.dark .focus-visible:focus {
  outline: 2px solid #00f7ff;
}

/* Animation pour les icônes */
.icon-hover {
  transition: transform 0.2s ease;
}

.icon-hover:hover {
  transform: scale(1.1) rotate(5deg);
}

/* Styles pour les alertes modernes */
.alert-modern {
  border-left: 4px solid;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
}

.dark .alert-modern {
  background: rgba(0, 0, 0, 0.3);
}