/* Styles pour le composant de liste des projets */

/* Animations pour les cartes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Effet glassmorphism */
.glass-card {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass-card {
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Animation pour les cartes de projet */
.project-card {
  animation: fadeInUp 0.6s ease-out;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.project-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.dark .project-card:hover {
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
}

/* Animation pour les boutons */
.btn-modern {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-modern:hover::before {
  left: 100%;
}

/* Styles pour les badges */
.badge-gradient {
  background: linear-gradient(135deg, #4f5fad 0%, #7826b5 100%);
  transition: all 0.3s ease;
}

.dark .badge-gradient {
  background: linear-gradient(135deg, #00f7ff 0%, #9d4edd 100%);
}

/* Animation pour les icônes */
.icon-hover {
  transition: transform 0.2s ease;
}

.icon-hover:hover {
  transform: scale(1.1) rotate(5deg);
}

/* Styles pour les tooltips */
.tooltip {
  position: relative;
}

.tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s;
  z-index: 1000;
}

.tooltip:hover::after {
  opacity: 1;
}

/* Animation pour les états de chargement */
.loading-pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Styles pour les états vides */
.empty-state {
  animation: fadeInUp 0.8s ease-out;
}

/* Amélioration des focus states pour l'accessibilité */
.focus-visible:focus {
  outline: 2px solid #4f5fad;
  outline-offset: 2px;
}

.dark .focus-visible:focus {
  outline: 2px solid #00f7ff;
}

/* Responsive design amélioré */
@media (max-width: 768px) {
  .project-card {
    margin-bottom: 1rem;
  }

  .btn-modern {
    width: 100%;
    justify-content: center;
  }
}

/* Animation pour les actions flottantes */
.floating-actions {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.floating-actions button {
  transition: all 0.2s ease;
}

.floating-actions button:hover {
  transform: scale(1.1);
}

/* Styles pour les fichiers */
.file-item {
  transition: all 0.2s ease;
}

.file-item:hover {
  transform: translateX(4px);
  background-color: rgba(79, 95, 173, 0.05);
}

.dark .file-item:hover {
  background-color: rgba(0, 247, 255, 0.05);
}

/* Animation pour la boîte de dialogue */
.dialog-overlay {
  animation: fadeIn 0.3s ease-out;
}

.dialog-content {
  animation: scaleIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Styles pour les gradients de fond */
.bg-gradient-modern {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.dark .bg-gradient-modern {
  background: linear-gradient(135deg, #00f7ff 0%, #9d4edd 100%);
}

/* Effet de survol pour les cartes */
.card-hover-effect {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover-effect:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.dark .card-hover-effect:hover {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}