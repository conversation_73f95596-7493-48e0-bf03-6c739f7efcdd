/* Container principal */
.evaluation-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f8fafc;
  min-height: 100vh;
}

/* En-tête */
.header-section {
  margin-bottom: 30px;
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.page-title::before {
  content: "📝";
  font-size: 1.5rem;
}

/* Breadcrumb */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  color: #64748b;
}

.breadcrumb-link {
  color: #3b82f6;
  text-decoration: none;
  transition: color 0.2s;
}

.breadcrumb-link:hover {
  color: #1d4ed8;
  text-decoration: underline;
}

.breadcrumb-separator {
  color: #94a3b8;
}

.breadcrumb-current {
  color: #1e293b;
  font-weight: 500;
}

/* Loading spinner */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-spinner p {
  color: #64748b;
  font-size: 1rem;
  margin: 0;
}

/* Message d'erreur */
.error-message {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 500;
}

.error-message i {
  font-size: 1.2rem;
}

/* Contenu principal */
.main-content {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

/* Carte d'informations sur le rendu */
.rendu-info-card {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  padding-bottom: 10px;
  border-bottom: 2px solid #f1f5f9;
}

.card-title i {
  color: #3b82f6;
  font-size: 1.1rem;
}

/* Grille d'informations */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.info-item label {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.info-item span {
  color: #1e293b;
  font-size: 1rem;
  padding: 8px 12px;
  background-color: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

/* Lien de fichier */
.file-link {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  color: #3b82f6;
  text-decoration: none;
  padding: 8px 12px;
  background-color: #eff6ff;
  border: 1px solid #bfdbfe;
  border-radius: 6px;
  transition: all 0.2s;
  font-weight: 500;
}

.file-link:hover {
  background-color: #dbeafe;
  border-color: #93c5fd;
  transform: translateY(-1px);
}

.file-link i {
  font-size: 0.875rem;
}

/* Formulaire d'évaluation */
.evaluation-form {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.form-card {
  padding: 25px;
}

/* Section des scores */
.scores-section {
  margin-bottom: 30px;
}

.score-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 25px;
  margin-bottom: 25px;
}

.score-item {
  background-color: #f8fafc;
  padding: 20px;
  border-radius: 10px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s;
}

.score-item:hover {
  border-color: #cbd5e1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.score-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 10px;
  font-size: 0.95rem;
}

.score-label i {
  color: #3b82f6;
  font-size: 1rem;
}

.score-input-container {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.score-input {
  flex: 1;
  padding: 12px 15px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  text-align: center;
  transition: all 0.2s;
  background-color: white;
}

.score-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.score-input:invalid {
  border-color: #ef4444;
}

.score-max {
  font-weight: 600;
  color: #64748b;
  font-size: 1rem;
}

.score-description {
  color: #64748b;
  font-size: 0.8rem;
  line-height: 1.4;
  font-style: italic;
}

/* Score total */
.score-total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-radius: 12px;
  text-align: center;
  margin-top: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.total-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-bottom: 8px;
}

.total-label {
  font-size: 1.1rem;
  font-weight: 500;
}

.total-value {
  font-size: 2rem;
  font-weight: 700;
}

.total-max {
  font-size: 1.1rem;
  opacity: 0.9;
}

.total-percentage {
  font-size: 1rem;
  opacity: 0.9;
  font-weight: 500;
}

/* Section des commentaires */
.comments-section {
  margin-bottom: 30px;
}

.comments-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
  font-size: 1rem;
}

.comments-label i {
  color: #3b82f6;
  font-size: 1rem;
}

.comments-textarea {
  width: 100%;
  padding: 15px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.95rem;
  line-height: 1.6;
  resize: vertical;
  min-height: 120px;
  transition: all 0.2s;
  font-family: inherit;
}

.comments-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.comments-textarea::placeholder {
  color: #9ca3af;
  font-style: italic;
}

/* Validation des formulaires */
.form-validation {
  margin-top: 5px;
}

.error-text {
  color: #ef4444;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Actions */
.actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 15px;
  padding-top: 25px;
  border-top: 1px solid #e2e8f0;
}

/* Boutons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 140px;
  justify-content: center;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
}

.btn-primary:active:not(:disabled) {
  transform: translateY(0);
}

.btn-secondary {
  background-color: #f8fafc;
  color: #64748b;
  border: 2px solid #e2e8f0;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #f1f5f9;
  border-color: #cbd5e1;
  color: #475569;
  transform: translateY(-1px);
}

.btn-secondary:active:not(:disabled) {
  transform: translateY(0);
}

.btn i {
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .evaluation-container {
    padding: 15px;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .score-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .actions {
    flex-direction: column-reverse;
    gap: 10px;
  }

  .btn {
    width: 100%;
  }

  .total-display {
    flex-direction: column;
    gap: 5px;
  }

  .total-value {
    font-size: 1.8rem;
  }
}

@media (max-width: 480px) {
  .evaluation-container {
    padding: 10px;
  }

  .rendu-info-card,
  .form-card {
    padding: 20px;
  }

  .page-title {
    font-size: 1.3rem;
  }

  .score-item {
    padding: 15px;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.main-content {
  animation: fadeIn 0.3s ease-out;
}

.score-item {
  animation: fadeIn 0.3s ease-out;
}

.score-item:nth-child(1) { animation-delay: 0.1s; }
.score-item:nth-child(2) { animation-delay: 0.2s; }
.score-item:nth-child(3) { animation-delay: 0.3s; }
.score-item:nth-child(4) { animation-delay: 0.4s; }

/* États de focus améliorés */
.score-input:focus,
.comments-textarea:focus {
  transform: translateY(-1px);
}

/* Amélioration de l'accessibilité */
.btn:focus,
.score-input:focus,
.comments-textarea:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Indicateur de score */
.score-input[value="5"] {
  background-color: #dcfce7;
  border-color: #16a34a;
  color: #15803d;
}

.score-input[value="4"] {
  background-color: #fef3c7;
  border-color: #d97706;
  color: #92400e;
}

.score-input[value="3"] {
  background-color: #fef3c7;
  border-color: #f59e0b;
  color: #d97706;
}

.score-input[value="2"] {
  background-color: #fed7aa;
  border-color: #ea580c;
  color: #c2410c;
}

.score-input[value="1"] {
  background-color: #fecaca;
  border-color: #dc2626;
  color: #b91c1c;
}

.score-input[value="0"] {
  background-color: #f3f4f6;
  border-color: #9ca3af;
  color: #6b7280;
}