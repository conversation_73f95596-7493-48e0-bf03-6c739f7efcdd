/* Styles pour le layout équipe moderne */

/* Animations et transitions */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes glow {
  0%,
  100% {
    box-shadow: 0 0 5px rgba(79, 95, 173, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(79, 95, 173, 0.6);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Layout principal */
.equipe-layout {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f4f8 0%, #e8f2ff 100%);
  position: relative;
  overflow: hidden;
}

.dark .equipe-layout {
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
}

/* Sidebar */
.sidebar {
  width: 320px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(79, 95, 173, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  animation: slideIn 0.5s ease-out;
}

.dark .sidebar {
  background: rgba(26, 26, 26, 0.95);
  border-right: 1px solid rgba(0, 247, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Navigation items */
.nav-item {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-item:hover {
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(79, 95, 173, 0.2);
}

.dark .nav-item:hover {
  box-shadow: 0 4px 12px rgba(0, 247, 255, 0.2);
}

.nav-item.active {
  background: linear-gradient(
    135deg,
    rgba(79, 95, 173, 0.1) 0%,
    rgba(120, 38, 181, 0.1) 100%
  );
  border-left: 3px solid #4f5fad;
}

.dark .nav-item.active {
  background: linear-gradient(
    135deg,
    rgba(0, 247, 255, 0.2) 0%,
    rgba(79, 95, 173, 0.2) 100%
  );
  border-left: 3px solid #00f7ff;
}

/* Icon effects */
.nav-icon {
  position: relative;
  transition: all 0.3s ease;
}

.nav-item:hover .nav-icon {
  transform: scale(1.1);
  filter: drop-shadow(0 0 8px rgba(79, 95, 173, 0.5));
}

.dark .nav-item:hover .nav-icon {
  filter: drop-shadow(0 0 8px rgba(0, 247, 255, 0.5));
}

/* Statistics section */
.stats-section {
  background: linear-gradient(
    135deg,
    rgba(79, 95, 173, 0.05) 0%,
    rgba(120, 38, 181, 0.05) 100%
  );
  border-radius: 12px;
  padding: 16px;
  margin: 16px 0;
  border: 1px solid rgba(79, 95, 173, 0.1);
}

.dark .stats-section {
  background: linear-gradient(
    135deg,
    rgba(0, 247, 255, 0.1) 0%,
    rgba(79, 95, 173, 0.1) 100%
  );
  border: 1px solid rgba(0, 247, 255, 0.2);
}

.stat-item {
  padding: 8px 0;
  border-bottom: 1px solid rgba(79, 95, 173, 0.1);
  transition: all 0.3s ease;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-item:hover {
  background: rgba(79, 95, 173, 0.05);
  border-radius: 8px;
  padding-left: 8px;
}

.dark .stat-item:hover {
  background: rgba(0, 247, 255, 0.1);
}

/* Main content area */
.main-content {
  flex: 1;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border-radius: 20px 0 0 20px;
  margin: 20px 0 20px 0;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  animation: fadeIn 0.6s ease-out;
}

.dark .main-content {
  background: rgba(26, 26, 26, 0.8);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Header */
.header {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(240, 244, 248, 0.9) 100%
  );
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(79, 95, 173, 0.2);
}

.dark .header {
  background: linear-gradient(
    135deg,
    rgba(26, 26, 26, 0.9) 0%,
    rgba(10, 10, 10, 0.9) 100%
  );
  border-bottom: 1px solid rgba(0, 247, 255, 0.2);
}

/* Search input */
.search-input {
  background: rgba(240, 244, 248, 0.8);
  border: 1px solid rgba(79, 95, 173, 0.2);
  border-radius: 12px;
  padding: 12px 16px 12px 40px;
  transition: all 0.3s ease;
}

.search-input:focus {
  background: rgba(255, 255, 255, 0.9);
  border-color: #4f5fad;
  box-shadow: 0 0 0 3px rgba(79, 95, 173, 0.1);
  transform: scale(1.02);
}

.dark .search-input {
  background: rgba(10, 10, 10, 0.8);
  border: 1px solid rgba(0, 247, 255, 0.2);
}

.dark .search-input:focus {
  background: rgba(26, 26, 26, 0.9);
  border-color: #00f7ff;
  box-shadow: 0 0 0 3px rgba(0, 247, 255, 0.1);
}

/* Buttons */
.btn-primary {
  background: linear-gradient(135deg, #4f5fad 0%, #7826b5 100%);
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  color: white;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 95, 173, 0.4);
}

.dark .btn-primary {
  background: linear-gradient(135deg, #00f7ff 0%, #4f5fad 100%);
}

.dark .btn-primary:hover {
  box-shadow: 0 8px 25px rgba(0, 247, 255, 0.4);
}

.btn-secondary {
  background: rgba(109, 104, 112, 0.2);
  border: 1px solid rgba(109, 104, 112, 0.3);
  border-radius: 12px;
  padding: 12px 24px;
  color: #6d6870;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: rgba(109, 104, 112, 0.3);
  transform: translateY(-1px);
}

.dark .btn-secondary {
  background: rgba(160, 160, 160, 0.2);
  border: 1px solid rgba(160, 160, 160, 0.3);
  color: #e0e0e0;
}

.dark .btn-secondary:hover {
  background: rgba(160, 160, 160, 0.3);
}
