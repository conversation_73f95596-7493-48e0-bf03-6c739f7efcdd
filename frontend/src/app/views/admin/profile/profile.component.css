.profile-card {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden border border-gray-100 dark:border-gray-700;
}

.profile-header {
  @apply relative;
}
.profile-header .cover-photo {
  @apply h-32 w-full object-cover;
}
.profile-header .profile-photo-container {
  @apply absolute bottom-0 left-6 transform translate-y-1/2;
}
.profile-header .profile-photo-container .profile-photo {
  @apply w-20 h-20 rounded-full border-2 border-white dark:border-gray-800 object-cover bg-primary-light dark:bg-primary-dark text-white flex items-center justify-center text-2xl shadow-sm;
}
.profile-header .profile-photo-container .upload-overlay {
  @apply absolute inset-0 bg-black bg-opacity-50 rounded-full opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center cursor-pointer;
}
.profile-header .profile-photo-container .upload-overlay svg {
  @apply w-5 h-5 text-white;
}

.profile-content {
  @apply pt-14 px-6 pb-6;
}
.profile-content .profile-name {
  @apply text-xl font-semibold text-gray-900 dark:text-white;
}
.profile-content .profile-role {
  @apply text-xs text-gray-500 dark:text-gray-400 mb-4;
}

.profile-section {
  @apply mt-6;
}
.profile-section .section-title {
  @apply text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center;
}
.profile-section .section-title svg {
  @apply w-4 h-4 mr-1.5 text-primary-light dark:text-primary-dark;
}
.profile-section .info-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-3;
}
.profile-section .info-item {
  @apply bg-gray-50 dark:bg-gray-700/50 p-3 rounded-md border border-gray-100 dark:border-gray-700;
}
.profile-section .info-item .info-label {
  @apply text-xs text-gray-500 dark:text-gray-400;
}
.profile-section .info-item .info-value {
  @apply text-gray-900 dark:text-white text-sm font-medium;
}

.action-buttons {
  @apply flex flex-col sm:flex-row gap-2 mt-6;
}
.action-buttons .btn {
  @apply px-3 py-1.5 rounded-md text-sm font-medium transition-colors focus:outline-none focus:ring-1 focus:ring-offset-1;
}
.action-buttons .btn.btn-primary {
  @apply bg-primary-light dark:bg-primary-dark text-white hover:bg-opacity-90 focus:ring-primary-light dark:focus:ring-primary-dark;
}
.action-buttons .btn.btn-secondary {
  @apply bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600 focus:ring-gray-200 dark:focus:ring-gray-700 border border-gray-200 dark:border-gray-600;
}
.action-buttons .btn.btn-danger {
  @apply bg-red-50 text-red-600 dark:bg-red-900/20 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/30 focus:ring-red-200 dark:focus:ring-red-800 border border-red-200 dark:border-red-800;
}

.badge {
  @apply inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium;
}
.badge.verified {
  @apply bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300;
}
.badge.not-verified {
  @apply bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300;
}
.badge.active {
  @apply bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300;
}
.badge.inactive {
  @apply bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300;
}
