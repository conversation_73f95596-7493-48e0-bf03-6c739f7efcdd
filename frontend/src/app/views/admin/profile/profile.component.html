<!-- Begin Page Content -->
<div class="container-fluid p-4 md:p-6 bg-[#edf1f4] min-h-screen">
  <!-- Page Heading -->
  <div
    class="flex flex-col md:flex-row md:items-center md:justify-between mb-6"
  >
    <h1 class="text-2xl font-bold text-[#4f5fad] mb-2 md:mb-0">My Profile</h1>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="flex justify-center items-center py-20">
    <div
      class="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-[#4f5fad]"
    ></div>
  </div>

  <!-- Error Message -->
  <div
    *ngIf="error"
    class="bg-[#ff6b69]/10 border border-[#ff6b69] text-[#ff6b69] px-4 py-3 rounded-lg mb-6 animate-pulse"
  >
    {{ error }}
  </div>

  <!-- Success Message -->
  <div
    *ngIf="message"
    class="bg-[#afcf75]/10 border border-[#afcf75] text-[#2a5a03] px-4 py-3 rounded-lg mb-6 animate-pulse"
  >
    {{ message }}
  </div>

  <!-- Admin Profile -->
  <div
    *ngIf="!loading && user"
    class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8"
  >
    <!-- Profile Card -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden">
      <div class="relative">
        <img
          src="https://images.unsplash.com/photo-1579546929518-9e396f3cc809?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80"
          alt="Cover"
          class="w-full h-32 object-cover"
        />
        <div class="absolute left-0 right-0 -bottom-12 flex justify-center">
          <div
            class="h-24 w-24 rounded-full border-4 border-white overflow-hidden flex items-center justify-center"
            style="min-height: 96px; min-width: 96px"
          >
            <img
              *ngIf="!previewUrl || uploadLoading"
              [src]="
                user.profileImage &&
                user.profileImage !== 'null' &&
                user.profileImage.trim() !== ''
                  ? user.profileImage
                  : 'assets/images/default-profile.png'
              "
              alt="Profile"
              class="h-full w-full object-cover"
            />
            <img
              *ngIf="previewUrl && !uploadLoading"
              [src]="previewUrl"
              alt="Preview"
              class="h-full w-full object-cover"
            />
          </div>
          <label
            for="profile-upload"
            class="absolute bottom-0 right-0 bg-[#7826b5] text-white p-1.5 rounded-full cursor-pointer hover:bg-[#5f1d8f] transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M4 5a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V7a2 2 0 00-2-2h-1.586a1 1 0 01-.707-.293l-1.121-1.121A2 2 0 0011.172 3H8.828a2 2 0 00-1.414.586L6.293 4.707A1 1 0 015.586 5H4zm6 9a3 3 0 100-6 3 3 0 000 6z"
                clip-rule="evenodd"
              />
            </svg>
          </label>
          <input
            type="file"
            id="profile-upload"
            class="hidden"
            accept="image/*"
            (change)="onFileSelected($event)"
          />
        </div>
      </div>
      <div class="p-5 pt-16 text-center">
        <h2 class="text-xl font-bold text-[#4f5fad] mb-1">
          {{ user.fullName }}
        </h2>
        <p class="mb-4">
          <span
            class="px-2 py-1 text-xs rounded-full bg-[#4f5fad]/10 text-[#4f5fad] font-medium"
          >
            {{ user.role | titlecase }}
          </span>
        </p>

        <!-- Personal Information -->
        <div class="mt-6 border-t border-[#edf1f4] pt-4">
          <h3
            class="flex items-center justify-center text-[#4f5fad] font-semibold mb-4"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-2"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                clip-rule="evenodd"
              />
            </svg>
            Account Information
          </h3>
          <div class="space-y-4">
            <div class="text-left px-2">
              <div class="text-sm font-medium text-[#6d6870]">Email</div>
              <div class="text-[#4f5fad]">{{ user.email }}</div>
            </div>
            <div class="flex space-x-4">
              <div class="flex-1 text-left px-2">
                <div class="text-sm font-medium text-[#6d6870]">Status</div>
                <div>
                  <span
                    class="px-2 py-1 text-xs rounded-full"
                    [ngClass]="
                      user.isActive !== false
                        ? 'bg-[#afcf75]/10 text-[#2a5a03]'
                        : 'bg-[#ff6b69]/10 text-[#ff6b69]'
                    "
                  >
                    {{ user.isActive !== false ? "Active" : "Inactive" }}
                  </span>
                </div>
              </div>
              <div class="flex-1 text-left px-2">
                <div class="text-sm font-medium text-[#6d6870]">
                  Verification
                </div>
                <div>
                  <span
                    class="px-2 py-1 text-xs rounded-full"
                    [ngClass]="
                      user.verified
                        ? 'bg-[#afcf75]/10 text-[#2a5a03]'
                        : 'bg-[#ff6b69]/10 text-[#ff6b69]'
                    "
                  >
                    {{ user.verified ? "Verified" : "Not Verified" }}
                  </span>
                </div>
              </div>
            </div>
            <div class="text-left px-2">
              <div class="text-sm font-medium text-[#6d6870]">Member Since</div>
              <div class="text-[#4f5fad]">
                {{ user.createdAt | date : "mediumDate" }}
              </div>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="mt-6 flex flex-wrap justify-center gap-3">
          <!-- upload image -->
          <button
            *ngIf="selectedImage"
            (click)="onUpload()"
            class="inline-flex items-center bg-[#7826b5] hover:bg-[#5f1d8f] text-white px-4 py-2 rounded-lg shadow transition-all"
            [disabled]="uploadLoading"
          >
            <span *ngIf="!uploadLoading" class="flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 mr-2"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z"
                  clip-rule="evenodd"
                />
              </svg>
              Upload
            </span>
            <!-- État de chargement -->
            <span
              *ngIf="uploadLoading"
              class="flex items-center justify-center"
            >
              <svg
                class="animate-spin mr-2 h-4 w-4 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  class="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  stroke-width="4"
                ></circle>
                <path
                  class="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              Uploading...
            </span>
          </button>
          <!-- remove image -->
          <button
            *ngIf="user.profileImage"
            (click)="removeProfileImage()"
            class="inline-flex items-center bg-[#ff6b69] hover:bg-[#e05554] text-white px-4 py-2 rounded-lg shadow transition-all"
            [disabled]="removeLoading"
          >
            <span *ngIf="!removeLoading" class="flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 mr-2"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                  clip-rule="evenodd"
                />
              </svg>
              Remove
            </span>
            <span
              *ngIf="removeLoading"
              class="flex items-center justify-center"
            >
              <svg
                class="animate-spin mr-2 h-4 w-4 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  class="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  stroke-width="4"
                ></circle>
                <path
                  class="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              Removing
            </span>
          </button>
          <!-- change password -->
          <button
            (click)="navigateTo('/change-password')"
            class="inline-flex items-center bg-[#4f5fad] hover:bg-[#3d4a85] text-white px-4 py-2 rounded-lg shadow transition-all"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-2"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
                clip-rule="evenodd"
              />
            </svg>
            Password
          </button>
          <!-- logout -->
          <button
            (click)="logout()"
            class="inline-flex items-center bg-[#ff6b69] hover:bg-[#e05554] text-white px-4 py-2 rounded-lg shadow transition-all"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-2"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M3 3a1 1 0 00-1 1v12a1 1 0 001 1h12a1 1 0 001-1V7.414l-4-4H3zm6 11a1 1 0 11-2 0 1 1 0 012 0zm2-5.5a.5.5 0 00-.5.5v3a.5.5 0 01-.5.5h-3a.5.5 0 010-1H9V9.5A1.5 1.5 0 0110.5 8h.5a.5.5 0 01.5.5z"
                clip-rule="evenodd"
              />
            </svg>
            Logout
          </button>
        </div>
      </div>
    </div>

    <!-- Admin Dashboard Summary -->
    <div class="lg:col-span-2 space-y-6">
      <!-- Stats Cards -->
      <div class="bg-white rounded-xl shadow-md overflow-hidden">
        <div class="p-5 border-b border-[#bdc6cc]">
          <h3 class="flex items-center font-bold text-[#4f5fad]">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-2"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"
              />
            </svg>
            User Statistics
          </h3>
        </div>
        <div class="p-5">
          <!-- User Status -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <div class="flex justify-between items-center mb-2">
                <div class="text-sm font-medium text-[#6d6870]">
                  Total Users
                </div>
                <div class="text-xl font-semibold text-[#4f5fad]">
                  {{ stats.totalUsers }}
                </div>
              </div>
              <div class="w-full bg-[#edf1f4] rounded-full h-2.5">
                <div
                  class="bg-[#4f5fad] h-2.5 rounded-full"
                  style="width: 100%"
                ></div>
              </div>
            </div>

            <div>
              <div class="flex justify-between items-center mb-2">
                <div class="text-sm font-medium text-[#6d6870]">Status</div>
                <div class="flex space-x-3 text-sm">
                  <span class="text-[#afcf75] font-medium"
                    >{{ stats.activeUsers }} Active</span
                  >
                  <span class="text-[#bdc6cc]">|</span>
                  <span class="text-[#ff6b69] font-medium"
                    >{{ stats.inactiveUsers }} Inactive</span
                  >
                </div>
              </div>
              <div
                class="w-full bg-[#edf1f4] rounded-full h-2.5 overflow-hidden flex"
              >
                <div
                  class="bg-[#afcf75] h-2.5"
                  [style.width.%]="
                    stats.totalUsers
                      ? (stats.activeUsers / stats.totalUsers) * 100
                      : 0
                  "
                ></div>
                <div
                  class="bg-[#ff6b69] h-2.5"
                  [style.width.%]="
                    stats.totalUsers
                      ? (stats.inactiveUsers / stats.totalUsers) * 100
                      : 0
                  "
                ></div>
              </div>
            </div>
          </div>

          <!-- User Roles -->
          <div>
            <div class="flex justify-between items-center mb-2">
              <div class="text-sm font-medium text-[#6d6870]">User Roles</div>
              <div class="flex space-x-3 text-sm">
                <span class="text-[#4a89ce] font-medium">{{
                  stats.students
                }}</span>
                <span class="text-[#bdc6cc]">|</span>
                <span class="text-[#7826b5] font-medium">{{
                  stats.teachers
                }}</span>
                <span class="text-[#bdc6cc]">|</span>
                <span class="text-[#4f5fad] font-medium">{{
                  stats.admins
                }}</span>
              </div>
            </div>
            <div
              class="w-full bg-[#edf1f4] rounded-full h-2.5 overflow-hidden flex"
            >
              <div
                class="bg-[#4a89ce] h-2.5"
                [style.width.%]="
                  stats.totalUsers
                    ? (stats.students / stats.totalUsers) * 100
                    : 0
                "
              ></div>
              <div
                class="bg-[#7826b5] h-2.5"
                [style.width.%]="
                  stats.totalUsers
                    ? (stats.teachers / stats.totalUsers) * 100
                    : 0
                "
              ></div>
              <div
                class="bg-[#4f5fad] h-2.5"
                [style.width.%]="
                  stats.totalUsers ? (stats.admins / stats.totalUsers) * 100 : 0
                "
              ></div>
            </div>
            <div class="flex justify-between mt-2 text-xs text-[#6d6870]">
              <span>Students</span>
              <span>Teachers</span>
              <span>Admins</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Activity -->
      <div class="bg-white rounded-xl shadow-md overflow-hidden">
        <div class="p-5 border-b border-[#bdc6cc]">
          <h3 class="flex items-center font-bold text-[#4f5fad]">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-2"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                clip-rule="evenodd"
              />
            </svg>
            Recent Activity
          </h3>
        </div>
        <div class="p-5">
          <div class="divide-y divide-[#edf1f4]">
            <div
              *ngFor="let activity of recentActivity"
              class="py-3 flex justify-between items-center"
            >
              <div class="flex items-center">
                <div class="w-2 h-2 rounded-full bg-[#4f5fad] mr-3"></div>
                <div>
                  <div class="text-sm text-[#4f5fad]">
                    {{ activity.action }}
                  </div>
                  <div class="text-xs text-[#6d6870]">
                    {{ activity.target }}
                  </div>
                </div>
              </div>
              <div class="text-xs text-[#bdc6cc]">
                {{ formatDate(activity.timestamp) }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="bg-white rounded-xl shadow-md overflow-hidden">
        <div class="p-5 border-b border-[#bdc6cc]">
          <h3 class="flex items-center font-bold text-[#4f5fad]">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-2"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M5 3a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2V5a2 2 0 00-2-2H5zm0 2h10v7h-2l-1 2H8l-1-2H5V5z"
                clip-rule="evenodd"
              />
            </svg>
            Quick Actions
          </h3>
        </div>
        <div class="p-5">
          <div class="flex flex-wrap gap-3">
            <button
              (click)="navigateTo('/admin/dashboard')"
              class="inline-flex items-center bg-[#7826b5] hover:bg-[#5f1d8f] text-white px-4 py-2 rounded-lg shadow transition-all"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 mr-2"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"
                />
              </svg>
              Users
            </button>
            <button
              (click)="navigateTo('/')"
              class="inline-flex items-center bg-[#4a89ce] hover:bg-[#3a6ca3] text-white px-4 py-2 rounded-lg shadow transition-all"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 mr-2"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"
                />
              </svg>
              Home
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- /.container-fluid -->
